import { useState } from "react";
import { NavLink, useLocation, useNavigate } from "react-router-dom";
import { Building2, MessageSquare, BarChart3, Plus, ChevronDown, ChevronRight, PanelLeftClose, Trash2 } from "lucide-react";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { AddBrandDialog, DeleteBrandDialog } from "@/components/features/brand";
import { BrandService } from "@/services";
import { useToast } from "@/hooks/use-toast";

export function AppSidebar() {
  const { state } = useSidebar();
  const collapsed = state === "collapsed";
  const location = useLocation();
  const navigate = useNavigate();
  const currentPath = location.pathname;
  const [expandedBrands, setExpandedBrands] = useState<string[]>([]);
  const [brands, setBrands] = useState(BrandService.getAllBrands());
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [brandToDelete, setBrandToDelete] = useState<{ id: string; name: string } | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const { toast } = useToast();

  const isActive = (path: string) => currentPath === path;
  const getNavCls = ({ isActive }: { isActive: boolean }) =>
    isActive ? "bg-primary-light text-primary font-medium" : "hover:bg-secondary-hover";

  const toggleBrand = (brandId: string) => {
    setExpandedBrands(prev =>
      prev.includes(brandId)
        ? prev.filter(id => id !== brandId)
        : [...prev, brandId]
    );
  };

  const handleBrandToggle = (brandId: string, isOpen: boolean) => {
    setExpandedBrands(prev =>
      isOpen
        ? [...prev.filter(id => id !== brandId), brandId]
        : prev.filter(id => id !== brandId)
    );
  };

  const handleAddBrand = async (brandName: string) => {
    try {
      const newBrand = await BrandService.addBrand(brandName);
      setBrands(BrandService.getAllBrands());
      toast({
        title: "Brand added successfully",
        description: `${newBrand.name} has been added to your brands.`,
      });
    } catch (error) {
      toast({
        title: "Failed to add brand",
        description: "Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteClick = (brand: { id: string; name: string }) => {
    setBrandToDelete(brand);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteBrand = async () => {
    if (!brandToDelete) return;

    setIsDeleting(true);
    try {
      await BrandService.deleteBrand(brandToDelete.id);
      setBrands(BrandService.getAllBrands());

      // If the user is currently viewing the deleted brand, navigate to home
      if (currentPath.includes(`/brand/${brandToDelete.id}`)) {
        navigate('/');
      }

      toast({
        title: "Brand deleted successfully",
        description: `${brandToDelete.name} has been removed.`,
      });

      setIsDeleteDialogOpen(false);
      setBrandToDelete(null);
    } catch (error) {
      toast({
        title: "Failed to delete brand",
        description: "Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <Sidebar className={collapsed ? "w-14" : "w-64"} collapsible="icon">
      <SidebarContent>
        {/* Header */}
        <div className="p-4 border-b">
          <NavLink
            to="/"
            className="flex items-center gap-3 hover:bg-secondary-hover rounded-lg p-2 -m-2 transition-colors duration-200 group"
            aria-label="Go to home page"
          >
            <div className="w-8 h-8 bg-gradient-ai rounded-lg flex items-center justify-center group-hover:shadow-md transition-shadow duration-200">
              <MessageSquare className="w-5 h-5 text-ai-foreground" />
            </div>
            {!collapsed && (
              <div>
                <h2 className="font-bold text-foreground group-hover:text-primary transition-colors duration-200">Brand Response AI</h2>
                <p className="text-xs text-muted-foreground">Multi-brand management</p>
              </div>
            )}
          </NavLink>
        </div>

        {/* Brands */}
        <SidebarGroup>
          <SidebarGroupLabel className="flex items-center justify-between">
            <span>Brands</span>
            {!collapsed && (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={() => setIsAddDialogOpen(true)}
              >
                <Plus className="h-3 w-3" />
              </Button>
            )}
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {brands.map((brand) => {
                const isExpanded = expandedBrands.includes(brand.id);
                return (
                  <Collapsible
                    key={brand.id}
                    open={isExpanded && !collapsed}
                    onOpenChange={(isOpen) => handleBrandToggle(brand.id, isOpen)}
                  >
                    <SidebarMenuItem className="group">
                      <div className="flex items-center">
                        <CollapsibleTrigger asChild>
                          <SidebarMenuButton className="flex-1 justify-between">
                            <div className="flex items-center gap-2">
                              <Building2 className="h-4 w-4" />
                              {!collapsed && (
                                <div className="flex-1 flex items-center justify-between">
                                  <span className="truncate">{brand.name}</span>
                                  <div className="flex items-center gap-1">
                                    {brand.pendingReplies > 0 && (
                                      <Badge
                                        variant="secondary"
                                        className="h-5 px-1.5 text-xs bg-primary-light text-primary"
                                      >
                                        {brand.pendingReplies}
                                      </Badge>
                                    )}
                                    {brand.status === "paused" && (
                                      <div className="w-2 h-2 rounded-full bg-warning" />
                                    )}
                                  </div>
                                </div>
                              )}
                            </div>
                            {!collapsed && (
                              isExpanded
                                ? <ChevronDown className="h-3 w-3" />
                                : <ChevronRight className="h-3 w-3" />
                            )}
                          </SidebarMenuButton>
                        </CollapsibleTrigger>
                        {!collapsed && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200 ml-1"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteClick({ id: brand.id, name: brand.name });
                            }}
                            title={`Delete ${brand.name}`}
                          >
                            <Trash2 className="h-3 w-3 text-destructive" />
                          </Button>
                        )}
                      </div>

                      <CollapsibleContent>
                        <SidebarMenuSub>
                          <SidebarMenuSubItem>
                            <SidebarMenuSubButton asChild>
                              <NavLink
                                to={`/brand/${brand.id}/analytics`}
                                className={({ isActive }) => getNavCls({ isActive })}
                              >
                                <BarChart3 className="h-3 w-3" />
                                <span>Analytics</span>
                              </NavLink>
                            </SidebarMenuSubButton>
                          </SidebarMenuSubItem>
                          <SidebarMenuSubItem>
                            <SidebarMenuSubButton asChild>
                              <NavLink
                                to={`/brand/${brand.id}/messages`}
                                className={({ isActive }) => getNavCls({ isActive })}
                              >
                                <MessageSquare className="h-3 w-3" />
                                <span>Messages</span>
                              </NavLink>
                            </SidebarMenuSubButton>
                          </SidebarMenuSubItem>
                        </SidebarMenuSub>
                      </CollapsibleContent>
                    </SidebarMenuItem>
                  </Collapsible>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarTrigger className="w-full justify-start">
              <PanelLeftClose className="h-4 w-4" />
              {!collapsed && <span>Collapse Sidebar</span>}
            </SidebarTrigger>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>

      <AddBrandDialog
        open={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
        onAddBrand={handleAddBrand}
      />

      <DeleteBrandDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onDeleteBrand={handleDeleteBrand}
        brandName={brandToDelete?.name || ""}
        isDeleting={isDeleting}
      />
    </Sidebar>
  );
}
